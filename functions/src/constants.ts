/* eslint-disable no-unused-vars */
import { CORS_CONFIG } from "./config";

export const APP_NAME = "PREM";

export const MIN_TRANSACTION_THRESHOLD_TON = 0.9; // Minimum transaction amount to process (> 0.9 TON)

export const BPS_DIVISOR = 10000; // 1 BPS = 0.01%, so divide by 10000 to get decimal

export const MIN_REVENUE_BALANCE_ON_WALLET = 10;

export const BOT_HEALTH_CHECK_ENDPOINT = "/healthcheck";

export const commonFunctionsConfig = {
  cors: CORS_CONFIG,
  region: "europe-central2",
};

export enum LogOperations {
  // Order operations
  CREATE_BUYER_ORDER = "create_buyer_order",
  CREATE_SELLER_ORDER = "create_seller_order",
  BUYER_PURCHASE = "buyer_purchase",
  SELLER_PURCHASE = "seller_purchase",
  CANCEL_ORDER = "cancel_order",
  FULFILL_AND_RESELL = "fulfill_and_resell",
  ORDER_PROCESSING = "order_processing",
  CREATE_ORDER_AS_BUYER = "create_order_as_buyer",
  CREATE_ORDER_AS_SELLER = "create_order_as_seller",
  PURCHASE_AS_SELLER = "purchase_as_seller",
  CANCEL_USER_ORDER = "cancel_user_order",
  CANCELLATION_STARTED = "cancellation_started",
  CANCELLATION_COMPLETED = "cancellation_completed",
  CREATE_TRANSACTION_RECORD = "create_transaction_record",

  // Transaction operations
  TRANSACTION_PROCESSING = "transaction_processing",
  TRANSACTION_RECORD_CREATED = "transaction_record_created",

  // Balance operations
  BALANCE_OPERATION = "balance_operation",
  BALANCE_UPDATE = "balance_update",
  INSUFFICIENT_FUNDS = "insufficient_funds",
  FUNDS_VALIDATION = "funds_validation",
  BALANCE_LOCK = "balance_lock",
  BALANCE_UNLOCK = "balance_unlock",

  // Fee operations
  FEE_PROCESSING = "fee_processing",
  FEE_APPLIED = "fee_applied",
  APP_CONFIG_FETCH = "app_config_fetch",
  ADMIN_USER_LOOKUP = "admin_user_lookup",
  REFERRAL_FEE = "referral_fee",
  CUSTOM_REFERRAL = "custom_referral",
  ORDER_REFERRAL = "order_referral",
  REFERRER_NOT_FOUND = "referrer_not_found",
  TOTAL_FEE = "total_fee",
  DEPOSIT_FEE = "deposit_fee",
  MARKETPLACE_REVENUE = "marketplace_revenue",
  FIXED_CANCEL_ORDER_FEE = "fixed_cancel_order_fee",
  WITHDRAWAL_FEE = "withdrawal_fee",
  RESELL_PURCHASE_FEE = "resell_purchase_fee",
  PURCHASE_FEE_WITH_REFERRAL = "purchase_fee_with_referral",

  // Gift operations
  GIFT_VALIDATION = "gift_validation",
  LINK_GIFT_TO_ORDER = "link_gift_to_order",
  LINK_GIFT_TO_ORDER_START = "link_gift_to_order_start",
  CREATE_SELL_ORDER_FROM_GIFT = "create_sell_order_from_gift",
  ORDER_VALIDATION = "order_validation",
  GIFT_LINKED_TO_ORDER = "gift_linked_to_order",
  LINK_GIFT_TO_ORDER_COMPLETE = "link_gift_to_order_complete",
  LINK_GIFT_TO_ORDER_ERROR = "link_gift_to_order_error",
  CREATE_SELL_ORDER_FROM_GIFT_START = "create_sell_order_from_gift_start",
  COLLECTION_VALIDATION = "collection_validation",
  ORDER_CREATED_FROM_GIFT = "order_created_from_gift",
  CREATE_SELL_ORDER_FROM_GIFT_COMPLETE = "create_sell_order_from_gift_complete",
  CREATE_SELL_ORDER_FROM_GIFT_ERROR = "create_sell_order_from_gift_error",
  SECONDARY_MARKET_OPERATION = "secondary_market_operation",
  SET_SECONDARY_PRICE = "set_secondary_price",
  SECONDARY_PURCHASE = "secondary_purchase",

  // Monitor operations
  MONITORING = "monitoring",
  MONITOR = "monitor",
  PROCESS_EXPIRED = "process_expired",

  // Deadline operations
  DEADLINE_ADDED = "deadline_added",
  NO_UPDATES_NEEDED = "no_updates_needed",
  BATCH_PROCESSED = "batch_processed",
  DEADLINES_COMPLETED = "deadlines_completed",

  // User operations
  USER_LOOKUP = "user_lookup",
  USER_PROFILE = "user_profile",
  REFERRER_POINTS = "referrer_points",

  // Bot operations
  BOT_OPERATION = "bot_operation",
  BOT_HEALTH_CHECK = "bot_health_check",

  // Withdrawal operations
  WITHDRAWAL_LIMIT_CHECK = "withdrawal_limit_check",
  WITHDRAWAL_TRACKING_UPDATE = "withdrawal_tracking_update",
  WITHDRAW_FUNCTION = "withdraw_function",
  CHECK_WITHDRAWAL_LIMIT = "check_withdrawal_limit",
  UPDATE_WITHDRAWAL_TRACKING = "update_withdrawal_tracking",

  // TX operations
  GET_TX_LOOKUP = "get_tx_lookup",
  UPDATE_TX_LOOKUP = "update_tx_lookup",
  TRANSACTION_HISTORY = "transaction_history",

  // TON Monitor operations
  TRANSACTION = "transaction",
  TRANSACTION_FILTERING = "transaction_filtering",
  TON_API_CALL = "ton_api_call",
  TRANSACTION_EXTRACTION = "transaction_extraction",
  DEPOSIT_PROCESSING = "deposit_processing",
  TON_WALLET = "ton_wallet",

  // Revenue operations
  REVENUE_FUNCTION = "revenue_function",

  // Telegram operations
  TELEGRAM_AUTH = "telegram_auth",
  TELEGRAM_API = "telegram_api",
  FIREBASE_AUTH = "firebase_auth",

  // Collection operations
  COLLECTION_UPDATE = "collection_update",
  COLLECTION_CREATION = "collection_creation",
  COLLECTION_PROCESSING = "collection_processing",
  COLLECTION_EXISTENCE = "collection_existence",

  // Admin operations
  ADMIN_COLLECTION = "admin_collection",
  RECALCULATE_DEADLINES = "recalculate_deadlines",
  CLEAR_DEADLINES = "clear_deadlines",

  // Revenue operations
  REVENUE_WITHDRAWAL = "revenue_withdrawal",

  // Utility operations
  SAFE_DIVIDE = "safe_divide",
  COUNTER_SERVICE = "counter_service",

  // TON operations
  TON_MONITOR = "ton_monitor",
}
