import { createServiceLogger } from "../logger/base-logger";
import { LogOperations } from "../constants";

// Create service logger using functional composition
const telegramAuthLogger = createServiceLogger("telegram-auth-function");

export function logAuthStarted(): void {
  telegramAuthLogger.logInfo(
    "Starting Telegram authentication",
    LogOperations.TELEGRAM_AUTH,
    {}
  );
}

export function logValidationResult(data: {
  isValid: boolean;
  requestData: any;
}) {
  telegramAuthLogger.logInfo(
    "Telegram data validation result",
    LogOperations.TELEGRAM_AUTH,
    {
      isValid: data.isValid,
      requestData: data.requestData,
    }
  );
}

export function logUserCreated(data: { userId: string; telegramId: string }) {
  telegramAuthLogger.logInfo(
    `New Telegram user created: ${data.userId}`,
    LogOperations.TELEGRAM_AUTH,
    {
      userId: data.userId,
      action: "user_created",
    }
  );
}

export function logAuthError(data: { error: unknown; requestData: any }) {
  telegramAuthLogger.logError(
    "Telegram authentication error",
    data.error,
    LogOperations.TELEGRAM_AUTH,
    {
      requestData: data.requestData,
    }
  );
}

export function logDevelopmentMode() {
  telegramAuthLogger.logInfo(
    "Using development mode with mock data",
    LogOperations.TELEGRAM_AUTH,
    { mode: "development" }
  );
}

export function logMockUserParsed(data: {
  telegramId: string;
  username?: string;
  firstName?: string;
}) {
  telegramAuthLogger.logInfo(
    "Mock user data parsed",
    LogOperations.TELEGRAM_AUTH,
    data
  );
}

export function logValidationStarted() {
  telegramAuthLogger.logInfo(
    "Validating Telegram data",
    LogOperations.TELEGRAM_AUTH,
    {
      mode: "production",
    }
  );
}

export function logFirebaseUserFound(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Found existing Firebase Auth user",
    LogOperations.FIREBASE_AUTH,
    data
  );
}

export function logFirebaseUserCreating(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Creating new Firebase Auth user",
    LogOperations.FIREBASE_AUTH,
    data
  );
}

export function logFirebaseUserCreated(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Firebase Auth user created successfully",
    LogOperations.FIREBASE_AUTH,
    data
  );
}

export function logCustomClaimsSet(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Custom claims set successfully",
    LogOperations.FIREBASE_AUTH,
    data
  );
}

export function logAppConfigLoaded(data: { config: any }) {
  telegramAuthLogger.logInfo(
    "App config loaded",
    LogOperations.FIREBASE_AUTH,
    data
  );
}

export function logCustomTokenCreated(data: { userId: string }) {
  telegramAuthLogger.logInfo(
    "Custom token created successfully",
    LogOperations.FIREBASE_AUTH,
    data
  );
}

export function logFirebaseAuthError(data: { error: unknown; userId: string }) {
  telegramAuthLogger.logError(
    "Firebase Auth user creation error",
    data.error,
    LogOperations.FIREBASE_AUTH,
    { userId: data.userId }
  );
}
