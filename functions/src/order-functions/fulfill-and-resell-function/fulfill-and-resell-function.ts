import { HttpsError, onCall } from "firebase-functions/v2/https";
import { commonFunctionsConfig } from "../../constants";
import {
  requireAuthentication,
  getUserData,
} from "../../services/auth-middleware";
import { fulfillAndResellOrder } from "./fulfill-and-resell-function.service";
import {
  throwInvalidParameters,
  throwFulfillAndResellInternalError,
} from "./fulfill-and-resell-function.error-handler";
import { validateTelegramIdForOrderOperation } from "../../services/telegram-validation-service";
import { FulfillAndResellLogger } from "./fulfill-and-resell-function.logger";

export const fulfillOrderAndCreateResellOrder = onCall<{
  orderId: string;
  price: number;
}>(commonFunctionsConfig, async (request) => {
  const authRequest = requireAuthentication(request);
  const { orderId, price } = request.data;
  const userId = authRequest.auth.uid;

  if (!orderId || !price || price <= 0) {
    throwInvalidParameters();
  }

  try {
    // Validate user has telegram ID for fulfill and resell operations
    const user = await getUserData(userId);
    validateTelegramIdForOrderOperation(user, "fulfill and resell orders");

    FulfillAndResellLogger.logFulfillAndResellStarted({
      orderId,
      userId,
      price,
    });

    const result = await fulfillAndResellOrder({ orderId, price, userId });

    FulfillAndResellLogger.logFulfillAndResellSuccess({
      originalOrderId: result.originalOrderId,
      newOrderId: result.newOrderId,
      userId,
      price: result.price,
      lockAmount: result.lockAmount,
    });

    return result;
  } catch (error) {
    logFulfillAndResellError({ error, orderId, userId, price });

    // Re-throw HttpsError as-is
    if (error instanceof HttpsError) {
      throw error;
    }

    throwFulfillAndResellInternalError((error as any).message);
  }
});
