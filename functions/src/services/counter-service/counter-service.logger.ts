import { createServiceLogger } from "../../logger/base-logger";
import { LogOperations } from "../../constants";

// Create service logger using functional composition
const counterServiceLogger = createServiceLogger("counter-service");

export function logCounterIncremented({
  counterName,
  newValue,
}: {
  counterName: string;
  newValue: number;
}) {
  counterServiceLogger.logInfo(
    "Counter incremented",
    LogOperations.COUNTER_SERVICE,
    {
      counterName,
      newValue,
    }
  );
}

export function logCounterServiceError({
  error,
  operation,
  counterName,
}: {
  error: unknown;
  operation: string;
  counterName?: string;
}) {
  counterServiceLogger.logError(
    `Error in counter service: ${operation}`,
    error,
    operation,
    {
      counterName,
    }
  );
}
