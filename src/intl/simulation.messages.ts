import { defineMessages } from "@formatjs/intl";

export const simulationMessages = defineMessages({
  simulationOrderActivation: {
    id: "simulation.orderActivation",
    defaultMessage:
      "\n\n🔧 DEV MODE: This is a simulation. In production, you would need to deposit a gift first.",
  },
  simulationOrderView: {
    id: "simulation.orderView",
    defaultMessage: "🔧 DEV MODE: This order is in simulation mode.",
  },
  simulationGiftDepositSuccess: {
    id: "simulation.giftDepositSuccess",
    defaultMessage:
      "🎁 Gift Details:\n• Name: {giftName}\n• Model: {modelName}\n• Symbol: {symbolName}\n• Backdrop: {backdropName}\n\nYour gift is now available in the Pram app under 'My Gifts' tab.",
  },
  simulationGiftDepositError: {
    id: "simulation.giftDepositError",
    defaultMessage:
      "❌ Failed to deposit mock gift: {errorMessage}\n\nPlease try again or contact support if the issue persists.",
  },
  simulationGiftWithdrawalSuccess: {
    id: "simulation.giftWithdrawalSuccess",
    defaultMessage:
      "✅ Gift withdrawal completed successfully in simulation mode!\n\n🔧 SIMULATION MODE: Gift transfer skipped. In real mode, your gift would be transferred to you now.",
  },
  simulationGiftWithdrawalError: {
    id: "simulation.giftWithdrawalError",
    defaultMessage:
      "❌ Gift withdrawal failed in simulation mode: {errorMessage}\n\nPlease try again or contact support if the issue persists.",
  },
  simulationGiftDepositMode: {
    id: "simulation.giftDepositMode",
    defaultMessage:
      "🎁 Deposit a Gift (Simulation Mode)\n\n🔧 SIMULATION MODE: Generating and depositing a mock gift...",
  },
  simulationGiftWithdrawalMode: {
    id: "simulation.giftWithdrawalMode",
    defaultMessage:
      "🎁 Gift Withdrawal (Simulation Mode)\n\n🔧 SIMULATION MODE: Processing gift withdrawal...",
  },
  simulationSellerGiftDeposit: {
    id: "simulation.sellerGiftDeposit",
    defaultMessage:
      "\n\n🔧 DEV MODE: This is a simulation. In production, you would send the gift to @premrelayer.",
  },
});
