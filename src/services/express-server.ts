import express, { Request, Response, NextFunction } from "express";
import { Server } from "http";
import { HealthcheckService } from "./healthcheck";
import bot from "../bot";
import { addSecurityHeaders } from "../middleware/auth-middleware";
import { ExpressServerLogger } from "./express-server.logger";

export class ExpressHttpServer {
  private readonly app: express.Application;
  private server: Server | null = null;
  private readonly port: number;
  private isReady = false;

  constructor(port: number = 8080) {
    this.port = port;
    this.app = express();
    this.setupMiddleware();
    this.setupRoutes();
    this.setupErrorHandling();
  }

  setReady(ready: boolean): void {
    this.isReady = ready;
    ExpressServerLogger.logServerReadiness({
      ready,
    });
  }

  private setupMiddleware(): void {
    this.app.use(addSecurityHeaders);

    this.app.use(express.json({ limit: "10mb" }));

    this.app.use(express.urlencoded({ extended: true }));

    this.app.use((_req: Request, res: Response, next: NextFunction) => {
      res.header("Access-Control-Allow-Origin", "*");
      res.header("Access-Control-Allow-Methods", "GET, POST, OPTIONS");
      res.header("Access-Control-Allow-Headers", "Content-Type, Authorization");
      next();
    });

    this.app.use((req: Request, _res: Response, next: NextFunction) => {
      ExpressServerLogger.logHttpRequest({
        method: req.method,
        url: req.url,
      });
      next();
    });
  }

  private setupRoutes(): void {
    this.app.get(
      "/healthcheck",
      // authenticateHealthCheck,
      async (_req: Request, res: Response) => {
        try {
          const lastHealthcheck = await HealthcheckService.getLastHealthcheck();
          const isHealthy = await HealthcheckService.isHealthy();

          const response = {
            status: isHealthy ? "healthy" : "unhealthy",
            lastHealthcheck,
            timestamp: new Date().toISOString(),
            service: "marketplace-bot",
          };

          res.status(isHealthy ? 200 : 503).json(response);
        } catch (error) {
          ExpressServerLogger.logHealthcheckError({
            error,
          });

          const errorResponse = {
            status: "error",
            message: "Failed to check health status",
            timestamp: new Date().toISOString(),
            service: "marketplace-bot",
          };

          res.status(500).json(errorResponse);
        }
      }
    );

    this.app.get("/readiness", (_req: Request, res: Response) => {
      const response = {
        status: this.isReady ? "ready" : "not ready",
        timestamp: new Date().toISOString(),
        service: "marketplace-bot",
      };

      res.status(this.isReady ? 200 : 503).json(response);
    });

    this.app.post("/webhook", async (req: Request, res: Response) => {
      try {
        const update = req.body;

        ExpressServerLogger.logWebhookReceived({
          updateId: update.update_id,
        });

        console.log("update from webhook", JSON.stringify(update));

        await bot.handleUpdate(update);

        res.json({ ok: true });
      } catch (error) {
        ExpressServerLogger.logWebhookProcessingError({
          error,
        });
        res.status(500).json({ ok: false, error: "Failed to process update" });
      }
    });

    this.app.get("/", (_req: Request, res: Response) => {
      const response = {
        service: "marketplace-bot",
        status: "running",
        ready: this.isReady,
        timestamp: new Date().toISOString(),
        endpoints: ["/healthcheck", "/readiness", "/webhook"],
      };

      res.json(response);
    });

    this.app.use((req: Request, res: Response) => {
      const notFoundResponse = {
        error: "Not Found",
        message: `Route ${req.url} not found`,
        timestamp: new Date().toISOString(),
      };

      res.status(404).json(notFoundResponse);
    });
  }

  private setupErrorHandling(): void {
    this.app.use(
      (error: Error, req: Request, res: Response, _next: NextFunction) => {
        ExpressServerLogger.logRequestHandlingError({
          error,
          url: req.url,
          method: req.method,
        });

        if (!res.headersSent) {
          res.status(500).json({ error: "Internal Server Error" });
        }
      }
    );
  }

  start(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        this.server = this.app.listen(this.port, () => {
          ExpressServerLogger.logServerStarted({
            port: this.port,
          });
          resolve();
        });

        this.server.on("error", (error: Error) => {
          ExpressServerLogger.logServerError({
            error,
            port: this.port,
          });
          reject(error);
        });
      } catch (error) {
        reject(error);
      }
    });
  }

  stop(): Promise<void> {
    return new Promise((resolve) => {
      if (this.server) {
        ExpressServerLogger.logServerStopping();

        this.server.close(() => {
          ExpressServerLogger.logServerStopped();
          resolve();
        });
      } else {
        resolve();
      }
    });
  }
}

export const expressHttpServer = new ExpressHttpServer(8080);
