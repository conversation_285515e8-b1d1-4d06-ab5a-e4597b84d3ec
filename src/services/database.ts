import sqlite3 from "sqlite3";
import path from "path";
import { SupportedLocale } from "../i18n";

export interface UserLanguagePreference {
  tg_id: string;
  language: string;
  created_at: string;
  updated_at: string;
}

class DatabaseService {
  private db: sqlite3.Database | null = null;
  private readonly dbPath: string;

  constructor() {
    this.dbPath = path.join(process.cwd(), "data", "bot.db");
  }

  private executeQuery(
    query: string,
    params: any[],
    resolve: () => void,
    reject: (error: Error) => void
  ): void {
    if (!this.db) {
      reject(new Error("Database not initialized"));
      return;
    }

    this.db.run(query, params, (err) => {
      if (err) {
        reject(err);
        return;
      }
      resolve();
    });
  }

  private executeGet<T>(
    query: string,
    params: any[],
    resolve: (result: T) => void,
    reject: (error: Error) => void
  ): void {
    if (!this.db) {
      reject(new Error("Database not initialized"));
      return;
    }

    this.db.get(query, params, (err, row: T) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(row);
    });
  }

  private executeAll<T>(
    query: string,
    params: any[],
    resolve: (results: T[]) => void,
    reject: (error: Error) => void
  ): void {
    if (!this.db) {
      reject(new Error("Database not initialized"));
      return;
    }

    this.db.all(query, params, (err, rows: T[]) => {
      if (err) {
        reject(err);
        return;
      }
      resolve(rows || []);
    });
  }

  async initialize(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // Ensure data directory exists
        const fs = require("fs");
        const dataDir = path.dirname(this.dbPath);
        if (!fs.existsSync(dataDir)) {
          fs.mkdirSync(dataDir, { recursive: true });
        }

        // Initialize database
        this.db = new sqlite3.Database(this.dbPath, (err) => {
          if (err) {
            reject(err);
            return;
          }

          // Create tables
          this.db!.run(
            `
            CREATE TABLE IF NOT EXISTS user_language_preferences (
              tg_id TEXT PRIMARY KEY,
              language TEXT NOT NULL DEFAULT 'en',
              created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
              updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
            )
          `,
            (err) => {
              if (err) {
                reject(err);
                return;
              }
              console.log("Database initialized successfully");
              resolve();
            }
          );
        });
      } catch (error) {
        console.error("Failed to initialize database:", error);
        reject(error);
      }
    });
  }

  async setUserLanguage(
    tgId: string,
    language: SupportedLocale
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      if (!this.db) {
        reject(new Error("Database not initialized"));
        return;
      }

      // If language is English (default), delete the record to optimize storage
      if (language === SupportedLocale.EN) {
        this.executeQuery(
          `DELETE FROM user_language_preferences WHERE tg_id = ?`,
          [tgId],
          resolve,
          reject
        );
      } else {
        // Only store non-English language preferences
        this.executeQuery(
          `INSERT OR REPLACE INTO user_language_preferences (tg_id, language, updated_at)
           VALUES (?, ?, CURRENT_TIMESTAMP)`,
          [tgId, language],
          resolve,
          reject
        );
      }
    });
  }

  async getUserLanguage(tgId: string): Promise<SupportedLocale> {
    return new Promise((resolve, reject) => {
      this.executeGet<UserLanguagePreference | undefined>(
        `SELECT language FROM user_language_preferences WHERE tg_id = ?`,
        [tgId],
        (row) => {
          // Return stored language or default to English if no record exists
          resolve((row?.language as SupportedLocale) || SupportedLocale.EN);
        },
        reject
      );
    });
  }

  /**
   * Check if user has an explicit language preference stored
   * Returns true only if user has a non-English preference stored
   */
  async hasExplicitLanguagePreference(tgId: string): Promise<boolean> {
    return new Promise((resolve, reject) => {
      this.executeGet<any>(
        `SELECT 1 FROM user_language_preferences WHERE tg_id = ?`,
        [tgId],
        (row) => {
          // If record exists, user has explicit non-English preference
          resolve(!!row);
        },
        reject
      );
    });
  }

  async getAllUserLanguages(): Promise<UserLanguagePreference[]> {
    return new Promise((resolve, reject) => {
      this.executeAll<UserLanguagePreference>(
        `SELECT * FROM user_language_preferences ORDER BY created_at DESC`,
        [],
        resolve,
        reject
      );
    });
  }

  async deleteUserLanguage(tgId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      this.executeQuery(
        `DELETE FROM user_language_preferences WHERE tg_id = ?`,
        [tgId],
        resolve,
        reject
      );
    });
  }

  async close(): Promise<void> {
    return new Promise((resolve) => {
      if (this.db) {
        this.db.close((err) => {
          if (err) {
            console.error("Error closing database:", err);
          }
          this.db = null;
          resolve();
        });
      } else {
        resolve();
      }
    });
  }

  // Health check method
  async isHealthy(): Promise<boolean> {
    return new Promise((resolve) => {
      if (!this.db) {
        resolve(false);
        return;
      }

      this.db.get("SELECT 1", (err) => {
        if (err) {
          console.error("Database health check failed:", err);
          resolve(false);
          return;
        }
        resolve(true);
      });
    });
  }

  // Get database stats
  async getStats(): Promise<{
    totalUsers: number;
    languageDistribution: Record<string, number>;
  }> {
    const totalUsers = await this.getTotalUsers();
    const languageDistribution = await this.getLanguageDistribution();

    return {
      totalUsers,
      languageDistribution,
    };
  }

  private async getTotalUsers(): Promise<number> {
    return new Promise((resolve, reject) => {
      this.executeGet<{ count: number }>(
        `SELECT COUNT(*) as count FROM user_language_preferences`,
        [],
        (result) => resolve(result?.count || 0),
        reject
      );
    });
  }

  private async getLanguageDistribution(): Promise<Record<string, number>> {
    return new Promise((resolve, reject) => {
      this.executeAll<{ language: string; count: number }>(
        `SELECT language, COUNT(*) as count FROM user_language_preferences GROUP BY language`,
        [],
        (results) => {
          const distribution: Record<string, number> = {};
          results.forEach((row) => {
            distribution[row.language] = row.count;
          });
          resolve(distribution);
        },
        reject
      );
    });
  }
}

export const databaseService = new DatabaseService();
