import * as admin from "firebase-admin";
import { log } from "../utils/logger";

let firebaseApp: admin.app.App | null = null;

export function initializeFirebaseAdmin(): admin.app.App {
  if (firebaseApp) {
    return firebaseApp;
  }

  try {
    const nodeEnv = process.env.NODE_ENV ?? "development";

    let serviceAccountPath: string;
    if (nodeEnv === "production") {
      console.log("----------------------------PROD");
      serviceAccountPath =
        "./service-account-keys/marketplace-prod-serviceAccountKey.json";
    } else {
      // Use development service account for local and development environments
      serviceAccountPath =
        "./service-account-keys/marketplace-dev-serviceAccountKey.json";
      console.log("----------------------------DEV");
    }

    log.info("Initializing Firebase Admin SDK", {
      operation: "firebase_init",
      environment: nodeEnv,
      serviceAccountPath: serviceAccountPath.replace(process.cwd(), ""),
    });

    const serviceAccount = require(serviceAccountPath);

    firebaseApp = admin.initializeApp({
      credential: admin.credential.cert(serviceAccount),
    });

    // Configure Firestore settings
    const firestore = firebaseApp.firestore();
    firestore.settings({
      ignoreUndefinedProperties: true,
    });

    log.info("Firebase Admin SDK initialized successfully", {
      operation: "firebase_init",
      projectId: serviceAccount.project_id,
      environment: nodeEnv,
    });

    return firebaseApp;
  } catch (error) {
    log.error("Failed to initialize Firebase Admin SDK", error, {
      operation: "firebase_init",
    });
    throw new Error("Firebase initialization failed");
  }
}

export function getFirebaseApp(): admin.app.App {
  if (!firebaseApp) {
    return initializeFirebaseAdmin();
  }
  return firebaseApp;
}

export function getFirestore(): admin.firestore.Firestore {
  return getFirebaseApp().firestore();
}

export function getAuth(): admin.auth.Auth {
  return getFirebaseApp().auth();
}
