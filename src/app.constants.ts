import { APP_NAME } from "./constants/bot-commands";

export const CDN_URL = "https://cdn.changes.tg/gifts";

export const BOT_TOKEN = process.env.BOT_TOKEN;

export const PREM_RELAYER_USERNAME = `@${APP_NAME.toLowerCase()}relayer`;

export const PREM_CHANNEL = `@${APP_NAME.toLowerCase()}_channel`;
export const PREM_SUPPORT_OFFICIAL = `@${APP_NAME.toLowerCase()}_support_official`;

export const WEB_APP_URL = process.env.WEB_APP_URL as string;

export const FIREBASE_PROJECT_ID = process.env.FIREBASE_PROJECT_ID;

export const PORT = process.env.PORT ?? 8080;
export const NODE_ENV = process.env.NODE_ENV ?? "development";
export const WEBHOOK_URL = process.env.WEBHO<PERSON>_URL;

export type TelegramGift = {
  gift: {
    base_name: string;
    name: string;
    number: number;
    model: {
      name: string;
      sticker: Sticker;
      rarity_per_mille: number;
    };
    symbol: {
      name: string;
      sticker: Sticker & { needs_repainting?: boolean };
      rarity_per_mille: number;
    };
    backdrop: {
      name: string;
      colors: {
        center_color: number;
        edge_color: number;
        symbol_color: number;
        text_color: number;
      };
      rarity_per_mille: number;
    };
  };
  owned_gift_id: string;
  transfer_star_count: number;
  origin: string;
};

type Sticker = {
  width: number;
  height: number;
  emoji: string;
  is_animated: boolean;
  is_video: boolean;
  type: "custom_emoji";
  custom_emoji_id: string;
  file_id: string;
  file_unique_id: string;
  file_size: number;
  thumbnail: Thumbnail;
  thumb: Thumbnail;
};

type Thumbnail = {
  file_id: string;
  file_unique_id: string;
  file_size: number;
  width: number;
  height: number;
};

export type TelegramBusinessMessageContext = {
  update_id: number;
  business_message: {
    business_connection_id: string;
    message_id: number;
    from: {
      id: number;
      is_bot: boolean;
      first_name: string;
      username: string;
      language_code: string;
      is_premium: boolean;
    };
    chat: {
      id: number;
      first_name: string;
      username: string;
      type: "private" | "channel" | "supergroup" | "group";
    };
    date: number;
    unique_gift: TelegramGift;
  };
};
