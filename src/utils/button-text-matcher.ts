import { T, TNoContext } from "../i18n";
import { ButtonMessageIds } from "../constants/bot-commands";

export function getAllButtonTexts(messageId: string): string[] {
  const texts: string[] = [];

  // Get English text (default)
  texts.push(TNoContext(messageId));

  // Get Ukrainian text
  const mockUkContext = { userLanguage: "uk" };
  texts.push(T(mockUkContext, messageId));

  // Get pidors text
  const mockRuContext = { userLanguage: "ru" };
  texts.push(T(mockRuContext, messageId));

  // Remove duplicates and return
  return [...new Set(texts)];
}

export function createButtonTextPattern(messageId: string): RegExp {
  const texts = getAllButtonTexts(messageId);
  // Escape special regex characters and create pattern
  const escapedTexts = texts.map((text) =>
    text.replace(/[.*+?^${}()|[\]\\]/g, "\\$&")
  );
  return new RegExp(`^(${escapedTexts.join("|")})$`);
}

export const ButtonTextPatterns = {
  MY_GIFTS: createButtonTextPattern(ButtonMessageIds.MY_GIFTS),
  DEPOSIT_GIFT: createButtonTextPattern(ButtonMessageIds.DEPOSIT_GIFT),
  CONTACT_SUPPORT: createButtonTextPattern(ButtonMessageIds.CONTACT_SUPPORT),
} as const;
